import { createRouter, createWebHistory } from 'vue-router'
import Home from '../components/Home.vue'
import ComingSoon from '../components/ComingSoon.vue'
import PlainText from '../components/PlainText.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/coming-soon',
    name: 'ComingSoon',
    component: ComingSoon
  },
  {
    path: '/terms-of-service',
    name: 'TermsOfService',
    component: PlainText,
    props: { filePath: '/terms-of-service.txt' }
  },
  {
    path: '/privacy-policy',
    name: 'PrivacyPolicy',
    component: PlainText,
    props: { filePath: '/privacy-policy.txt' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
