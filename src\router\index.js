import { createRouter, createWebHistory } from 'vue-router'
import Home from '../components/Home.vue'
import ComingSoon from '../components/ComingSoon.vue'
import PlainText from '../components/PlainText.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/coming-soon',
    name: 'ComingSoon',
    component: ComingSoon
  },

  // Developers routes
  {
    path: '/developers/documentation',
    name: 'Documentation',
    component: () => import('../components/NotFound.vue')
  },
  {
    path: '/developers/quickstart-guides',
    name: 'QuickstartGuides',
    component: () => import('../components/NotFound.vue')
  },
  {
    path: '/developers/support',
    name: 'DeveloperSupport',
    component: () => import('../components/NotFound.vue')
  },

  // Sales routes
  {
    path: '/sales/regular-pricing',
    name: 'RegularPricing',
    component: () => import('../components/NotFound.vue')
  },
  {
    path: '/sales/business-pricing',
    name: 'BusinessPricing',
    component: () => import('../components/NotFound.vue')
  },
  {
    path: '/sales/tiered-pricing',
    name: 'TieredPricing',
    component: () => import('../components/NotFound.vue')
  },
  {
    path: '/sales/request-demo',
    name: 'RequestDemo',
    component: () => import('../components/NotFound.vue')
  },
  {
    path: '/sales/contact-sales',
    name: 'ContactSales',
    component: () => import('../components/NotFound.vue')
  },

  // Company documents routes
  {
    path: '/company/privacy-policy',
    name: 'PrivacyPolicy',
    component: PlainText,
    props: { filePath: '/privacy-policy.txt' }
  },
  {
    path: '/company/terms-of-service',
    name: 'TermsOfService',
    component: PlainText,
    props: { filePath: '/terms-of-service.txt' }
  },
  {
    path: '/company/support-policy',
    name: 'SupportPolicy',
    component: () => import('../components/NotFound.vue')
  },

  // Catch-all 404 route
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../components/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
