<template>
  <div class="coming-soon">
    <div class="coming-soon-content">
      <div class="logo-section">
        <img src="/assets/DeepNPC-Logo.png" alt="DeepNPC" class="logo" />
      </div>
      
      <div class="main-content">
        <h2 class="coming-soon-title">Coming Soon</h2>
        <p class="coming-soon-description">
          We're working hard to bring you something amazing.<br>
          Stay tuned for updates!
        </p>
        
        <div class="back-button-container">
          <button @click="goBack" class="back-btn">
            ← Back to Home
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const goBack = () => {
  window.history.back();
}
</script>

<style scoped>
.coming-soon {
  font-family: 'Kanit', sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.coming-soon-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 800px;
  width: 100%;
  gap: 3rem;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.logo {
  height: 64px;
  width: auto;
  filter: drop-shadow(0 10px 20px rgba(79, 70, 229, 0.3));
}

.brand-name {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 0;
}

.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.coming-soon-title {
  font-size: 4rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #a0a0a0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.coming-soon-description {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #a0a0a0;
  margin: 0;
}

.back-button-container {
  margin-top: 1rem;
}

.back-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-family: 'Kanit', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-btn:hover {
  background: #4338ca;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .coming-soon-title {
    font-size: 2.5rem;
  }
  
  .coming-soon-description {
    font-size: 1rem;
  }
  
  .logo {
    height: 48px;
  }
  
  .brand-name {
    font-size: 1.5rem;
  }
}
</style>
