#!/bin/bash

# DeepNPC Deployment Script
# This script builds and deploys your Vue.js app to nginx

set -e  # Exit on any error

echo "🚀 Starting DeepNPC deployment..."

# Configuration
DOMAIN="your-domain.com"  # Replace with your domain
WEB_ROOT="/var/www/dnpc"
NGINX_CONFIG="/etc/nginx/sites-available/dnpc"
BACKUP_DIR="/var/backups/dnpc"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root or with sudo
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root or with sudo"
   exit 1
fi

# Step 1: Build the application
print_status "Building Vue.js application..."
npm run build

if [ ! -d "dist" ]; then
    print_error "Build failed - dist directory not found"
    exit 1
fi

# Step 2: Create backup of existing deployment
if [ -d "$WEB_ROOT" ]; then
    print_status "Creating backup of existing deployment..."
    mkdir -p "$BACKUP_DIR"
    cp -r "$WEB_ROOT" "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)"
fi

# Step 3: Create web root directory
print_status "Setting up web root directory..."
mkdir -p "$WEB_ROOT"

# Step 4: Copy built files
print_status "Copying built files to web root..."
cp -r dist/* "$WEB_ROOT/"

# Step 5: Set proper permissions
print_status "Setting file permissions..."
chown -R www-data:www-data "$WEB_ROOT"
find "$WEB_ROOT" -type d -exec chmod 755 {} \;
find "$WEB_ROOT" -type f -exec chmod 644 {} \;

# Step 6: Setup nginx configuration
print_status "Setting up nginx configuration..."
cp nginx.conf "$NGINX_CONFIG"

# Replace placeholder domain in nginx config
sed -i "s/your-domain.com/$DOMAIN/g" "$NGINX_CONFIG"

# Enable the site
ln -sf "$NGINX_CONFIG" /etc/nginx/sites-enabled/dnpc

# Step 7: Test nginx configuration
print_status "Testing nginx configuration..."
nginx -t

if [ $? -ne 0 ]; then
    print_error "Nginx configuration test failed"
    exit 1
fi

# Step 8: Reload nginx
print_status "Reloading nginx..."
systemctl reload nginx

# Step 9: Check if nginx is running
if systemctl is-active --quiet nginx; then
    print_status "Nginx is running successfully"
else
    print_error "Nginx is not running"
    systemctl status nginx
    exit 1
fi

print_status "✅ Deployment completed successfully!"
print_status "Your site should now be available at: http://$DOMAIN"
print_warning "Don't forget to:"
echo "  1. Update DNS records to point to this server"
echo "  2. Set up SSL certificate (Let's Encrypt recommended)"
echo "  3. Configure firewall to allow HTTP/HTTPS traffic"
