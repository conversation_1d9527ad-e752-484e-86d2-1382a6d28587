<template>
  <div class="pricing-page">
    <!-- Header -->
    <header class="header">
      <nav class="nav">
        <div class="nav-left">
          <router-link to="/">
            <img src="/assets/DeepNPC-Logo.png" alt="DeepNPC" class="logo" />
          </router-link>
        </div>
        <div class="nav-center">
          <router-link to="/developers/documentation" class="nav-link">Docs</router-link>
          <router-link to="/sales/regular-pricing" class="nav-link active">Pricing</router-link>
        </div>
        <div class="nav-right">
          <button @click="goToComingSoon" class="dashboard-btn">Dashboard</button>
        </div>
      </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
      <div class="pricing-header">
        <h1 class="pricing-title">Choose Your Plan</h1>
        <p class="pricing-subtitle">Select the perfect plan for your game development needs</p>
      </div>

      <div class="carousel-container">
        <button class="carousel-btn prev" @click="scrollLeft" :disabled="scrollPosition <= 0">
          ←
        </button>
        <div class="pricing-carousel" ref="carousel" @scroll="updateScrollPosition">
          <div class="pricing-plans">
        <!-- Free Trial -->
        <div class="plan-card">
          <div class="plan-header">
            <h3 class="plan-name">Free Trial</h3>
            <p class="plan-description">Perfect for testing and simplified implementation of our services</p>
          </div>
          <div class="plan-pricing">
            <span class="price">$0</span>
            <span class="period">Start from $0/month</span>
          </div>
          <button class="plan-button secondary">Test for Free</button>
          <div class="plan-features">
            <div class="feature">
              <img src="/icons/npc-tier.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC Tier</span>
              <span class="feature-value">T1</span>
            </div>
            <div class="feature">
              <img src="/icons/story-content.svg" alt="" class="feature-icon">
              <span class="feature-text">Included global story content tokens / NPC</span>
              <span class="feature-value">50</span>
            </div>
            <div class="feature">
              <img src="/icons/npc-count.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC count</span>
              <span class="feature-value">1</span>
            </div>
            <div class="feature disabled">
              <img src="/icons/player-interaction.svg" alt="" class="feature-icon disabled">
              <span class="feature-text">Per-player interaction and relation mode</span>
              <img src="/icons/x.svg" alt="" class="feature-icon">
            </div>
            <div class="feature disabled">
              <img src="/icons/memory.svg" alt="" class="feature-icon disabled">
              <span class="feature-text">NPC Memory instancing (per-player)</span>
              <img src="/icons/x.svg" alt="" class="feature-icon">
            </div>
          </div>
        </div>

        <!-- Basic -->
        <div class="plan-card">
          <div class="plan-header">
            <h3 class="plan-name">Basic</h3>
            <p class="plan-description">A basic package for your simple hobby projects.</p>
          </div>
          <div class="plan-pricing">
            <span class="price">$5</span>
            <span class="period">Start from $5/month</span>
          </div>
          <button class="plan-button secondary">Upgrade now</button>
          <div class="plan-features">
            <div class="feature">
              <img src="/icons/npc-tier.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC Tier</span>
              <span class="feature-value">T1</span>
            </div>
            <div class="feature">
              <img src="/icons/story-content.svg" alt="" class="feature-icon">
              <span class="feature-text">Included global story content tokens / NPC</span>
              <span class="feature-value">50</span>
            </div>
            <div class="feature">
              <img src="/icons/npc-count.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC count</span>
              <span class="feature-value">5</span>
            </div>
            <div class="feature disabled">
              <img src="/icons/player-interaction.svg" alt="" class="feature-icon">
              <span class="feature-text">Per-player interaction and relation mode</span>
              <img src="/icons/x.svg" alt="" class="feature-icon">
            </div>
            <div class="feature disabled">
              <img src="/icons/memory.svg" alt="" class="feature-icon">
              <span class="feature-text">NPC Memory instancing (per-player)</span>
              <img src="/icons/x.svg" alt="" class="feature-icon">
            </div>
            <div class="feature">
              <img src="/icons/email-support.svg" alt="" class="feature-icon">
              <span class="feature-text">Email support</span>
              <span class="feature-value">Yes</span>
            </div>
          </div>
        </div>

        <!-- Pro -->
        <div class="plan-card featured">
          <div class="plan-header">
            <h3 class="plan-name">Pro</h3>
            <p class="plan-description">Unlock the full capabilities of our character systems for a lower price</p>
          </div>
          <div class="plan-pricing">
            <span class="price">$25</span>
            <span class="period">Start from $25/month</span>
          </div>
          <button class="plan-button primary">Upgrade now</button>
          <div class="plan-features">
            <div class="feature">
              <img src="/icons/npc-tier.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC Tier</span>
              <span class="feature-value">T2</span>
            </div>
            <div class="feature">
              <img src="/icons/story-content.svg" alt="" class="feature-icon">
              <span class="feature-text">Included global story content tokens / NPC</span>
              <span class="feature-value">110</span>
            </div>
            <div class="feature">
              <img src="/icons/npc-count.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC count</span>
              <span class="feature-value">10</span>
            </div>
            <div class="feature">
              <img src="/icons/player-interaction.svg" alt="" class="feature-icon">
              <span class="feature-text">Per-player interaction and relation mode</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/memory.svg" alt="" class="feature-icon">
              <span class="feature-text">NPC Memory instancing (per-player)</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/email-support.svg" alt="" class="feature-icon">
              <span class="feature-text">Email support</span>
              <span class="feature-value">Yes</span>
            </div>
          </div>
        </div>

        <!-- Pro Plus -->
        <div class="plan-card">
          <div class="plan-header">
            <h3 class="plan-name">Pro Plus</h3>
            <p class="plan-description">For your game projects, perfect for more advanced operations</p>
          </div>
          <div class="plan-pricing">
            <span class="price">$60</span>
            <span class="period">Start from $60/month</span>
          </div>
          <button class="plan-button secondary">Upgrade now</button>
          <div class="plan-features">
            <div class="feature">
              <img src="/icons/npc-tier.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC Tier</span>
              <span class="feature-value">T2</span>
            </div>
            <div class="feature">
              <img src="/icons/story-content.svg" alt="" class="feature-icon">
              <span class="feature-text">Included global story content tokens / NPC</span>
              <span class="feature-value">110</span>
            </div>
            <div class="feature">
              <img src="/icons/npc-count.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC count</span>
              <span class="feature-value">10</span>
            </div>
            <div class="feature">
              <img src="/icons/player-interaction.svg" alt="" class="feature-icon">
              <span class="feature-text">Per-player interaction and relation mode</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/memory.svg" alt="" class="feature-icon">
              <span class="feature-text">NPC Memory instancing (per-player)</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/email-support.svg" alt="" class="feature-icon">
              <span class="feature-text">Email support</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/priority-support.svg" alt="" class="feature-icon">
              <span class="feature-text">Priority Representative support</span>
              <span class="feature-value">Yes</span>
            </div>
          </div>
        </div>


        <div class="plan-card">
          <div class="plan-header">
            <h3 class="plan-name">Advanced</h3>
            <p class="plan-description">For advanced users, indie games, and mid-scale RPG games on game servers</p>
          </div>
          <div class="plan-pricing">
            <span class="price">$250</span>
            <span class="period">Start from $250/month</span>
          </div>
          <button class="plan-button secondary">Upgrade now</button>
          <div class="plan-features">
            <div class="feature">
              <img src="/icons/npc-tier.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC Tier</span>
              <span class="feature-value">T3</span>
            </div>
            <div class="feature">
              <img src="/icons/story-content.svg" alt="" class="feature-icon">
              <span class="feature-text">Included global story content tokens / NPC</span>
              <span class="feature-value">200</span>
            </div>
            <div class="feature">
              <img src="/icons/npc-count.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC count</span>
              <span class="feature-value">50</span>
            </div>
            <div class="feature">
              <img src="/icons/player-interaction.svg" alt="" class="feature-icon">
              <span class="feature-text">Per-player interaction and relation mode</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/memory.svg" alt="" class="feature-icon">
              <span class="feature-text">NPC Memory instancing (per-player)</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/email-support.svg" alt="" class="feature-icon">
              <span class="feature-text">Email support</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/priority-support.svg" alt="" class="feature-icon">
              <span class="feature-text">Priority Representative support</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/server.svg" alt="" class="feature-icon">
              <span class="feature-text">Dedicated, scalable server instance(s)</span>
              <span class="feature-value">Yes</span>
            </div>
          </div>
        </div>

        <!-- Enterprise -->
        <div class="plan-card">
          <div class="plan-header">
            <h3 class="plan-name">Enterprise</h3>
            <p class="plan-description">For organizations, we offer customization for our services</p>
          </div>
          <div class="plan-pricing">
            <span class="price">Custom</span>
            <span class="period">Start from $500/month</span>
          </div>
          <button class="plan-button secondary">Contact sales</button>
          <div class="plan-features">
            <div class="feature">
              <img src="/icons/npc-tier.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC Tier</span>
              <span class="feature-value">-</span>
            </div>
            <div class="feature">
              <img src="/icons/story-content.svg" alt="" class="feature-icon">
              <span class="feature-text">Included global story content tokens / NPC</span>
              <span class="feature-value">-</span>
            </div>
            <div class="feature">
              <img src="/icons/npc-count.svg" alt="" class="feature-icon">
              <span class="feature-text">Included NPC count</span>
              <span class="feature-value">-</span>
            </div>
            <div class="feature">
              <img src="/icons/player-interaction.svg" alt="" class="feature-icon">
              <span class="feature-text">Per-player interaction and relation mode</span>
              <span class="feature-value">-</span>
            </div>
            <div class="feature">
              <img src="/icons/memory.svg" alt="" class="feature-icon">
              <span class="feature-text">NPC Memory instancing (per-player)</span>
              <span class="feature-value">-</span>
            </div>
            <div class="feature">
              <img src="/icons/email-support.svg" alt="" class="feature-icon">
              <span class="feature-text">Email support</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/priority-support.svg" alt="" class="feature-icon">
              <span class="feature-text">Priority Representative support</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/server.svg" alt="" class="feature-icon">
              <span class="feature-text">Dedicated, scalable server instance(s)</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/ai-systems.svg" alt="" class="feature-icon">
              <span class="feature-text">Customized AI systems</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/sla.svg" alt="" class="feature-icon">
              <span class="feature-text">Service-level-agreement</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <img src="/icons/dedicated-support.svg" alt="" class="feature-icon">
              <span class="feature-text">Dedicated support representative</span>
              <span class="feature-value">Yes</span>
            </div>
          </div>
        </div>
          </div>
        </div>
        <button class="carousel-btn next" @click="scrollRight" :disabled="scrollPosition >= maxScroll">
          →
        </button>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const carousel = ref(null)
const scrollPosition = ref(0)
const maxScroll = ref(0)

const goToComingSoon = () => {
  router.push('/coming-soon')
}

const updateScrollPosition = () => {
  if (carousel.value) {
    scrollPosition.value = carousel.value.scrollLeft
    maxScroll.value = carousel.value.scrollWidth - carousel.value.clientWidth
  }
}

const scrollLeft = () => {
  if (carousel.value) {
    carousel.value.scrollBy({ left: -320, behavior: 'smooth' })
  }
}

const scrollRight = () => {
  if (carousel.value) {
    carousel.value.scrollBy({ left: 320, behavior: 'smooth' })
  }
}

onMounted(() => {
  updateScrollPosition()
  window.addEventListener('resize', updateScrollPosition)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScrollPosition)
})
</script>

<style scoped>
.pricing-page {
  font-family: 'Kanit', sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
}

/* Header Styles */
.header {
  padding: 1rem 2rem;
  position: relative;
  z-index: 10;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.nav-left {
  display: flex;
  align-items: center;
}

.logo {
  height: 32px;
  width: auto;
}

.nav-center {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 400;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: white;
}

.nav-right {
  display: flex;
  align-items: center;
}

.dashboard-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-family: 'Kanit', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dashboard-btn:hover {
  background: #4338ca;
}

/* Main Content */
.main {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.pricing-header {
  margin-bottom: 3rem;
  text-align: center;
}

.pricing-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.pricing-subtitle {
  font-size: 1.1rem;
  color: #a0a0a0;
  margin: 0;
}

.carousel-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.carousel-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  flex-shrink: 0;
  z-index: 2;
}

.carousel-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.carousel-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.pricing-carousel {
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
  flex: 1;
}

.pricing-carousel::-webkit-scrollbar {
  display: none;
}

.pricing-plans {
  display: flex;
  gap: 28px;
  padding: 1rem 0;
  min-width: max-content;
}

.plan-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 28px;
  transition: all 0.3s ease;
  width: 355px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  height: 779px;
}

.plan-card.featured {
  border-color: #4f46e5;
  background: rgba(79, 70, 229, 0.1);
}

.plan-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.2);
}

.plan-header {
  margin-bottom: 1.5rem;
}

.plan-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.plan-description {
  color: #a0a0a0;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

.plan-pricing {
  margin-bottom: 1.5rem;
}

.price {
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.period {
  display: block;
  color: #a0a0a0;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.plan-button {
  width: 100%;
  padding: 0.75rem;
  border-radius: 8px;
  font-family: 'Kanit', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  margin-bottom: 1.5rem;
}

.plan-button.primary {
  background: #4f46e5;
  color: white;
}

.plan-button.primary:hover {
  background: #4338ca;
}

.plan-button.secondary {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.plan-button.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
}

.plan-features {
  display: flex;
  flex-direction: column;
  gap: 0;
  flex: 1;
}

.feature {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  height: 34px;
  margin: 0;
  padding: 0;
  line-height: 1;
}

.feature-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
  color: #e5e5e5;
  font-size: 12px;
  line-height: 1;
  height: 18px;
  display: flex;
  align-items: center;
}

.feature-value {
  color: #a0a0a0;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
  font-size: 12px;
  line-height: 1;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.feature.disabled {
  color: #666;
}

.feature.disabled .feature-text {
  color: #666;
}

.feature.disabled .feature-value {
  color: #666;
}
.feature-icon.img.disabled {
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-center {
    order: 2;
  }

  .pricing-title {
    font-size: 2rem;
  }

  .pricing-subtitle {
    font-size: 1rem;
  }

  .carousel-container {
    gap: 0.5rem;
  }

  .carousel-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .plan-card {
    width: 320px;
    padding: 1.25rem;
  }

  .main {
    padding: 1rem;
  }
}
</style>
