<template>
  <div class="pricing-page">
    <!-- Header -->
    <header class="header">
      <nav class="nav">
        <div class="nav-left">
          <router-link to="/">
            <img src="/assets/DeepNPC-Logo.png" alt="DeepNPC" class="logo" />
          </router-link>
        </div>
        <div class="nav-center">
          <router-link to="/developers/documentation" class="nav-link">Docs</router-link>
          <router-link to="/sales/regular-pricing" class="nav-link active">Pricing</router-link>
        </div>
        <div class="nav-right">
          <button @click="goToComingSoon" class="dashboard-btn">Dashboard</button>
        </div>
      </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
      <div class="pricing-header">
        <h1 class="pricing-title">Organization settings</h1>
        <div class="pricing-tabs">
          <button class="tab active">General</button>
          <button class="tab">Billing</button>
          <button class="tab">Members</button>
        </div>
      </div>

      <div class="pricing-plans">
        <!-- Free Trial -->
        <div class="plan-card">
          <div class="plan-header">
            <h3 class="plan-name">Free Trial</h3>
            <p class="plan-description">Perfect for testing and simplified implementation of our services</p>
          </div>
          <div class="plan-pricing">
            <span class="price">$0</span>
            <span class="period">Start from $0/month</span>
          </div>
          <button class="plan-button secondary">Test for Free</button>
          <div class="plan-features">
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Included NPCs</span>
              <span class="feature-value">1</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Trained global story content (NPCs)</span>
              <span class="feature-value">50</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Requests per month</span>
              <span class="feature-value">1K</span>
            </div>
          </div>
        </div>

        <!-- Basic -->
        <div class="plan-card">
          <div class="plan-header">
            <h3 class="plan-name">Basic</h3>
            <p class="plan-description">Great for your simple hobby projects</p>
          </div>
          <div class="plan-pricing">
            <span class="price">$5</span>
            <span class="period">Start from $5/month</span>
          </div>
          <button class="plan-button secondary">Upgrade now</button>
          <div class="plan-features">
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Included NPCs</span>
              <span class="feature-value">1</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Trained global story content (NPCs)</span>
              <span class="feature-value">50</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Requests per month</span>
              <span class="feature-value">5K</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Email support</span>
              <span class="feature-value">Yes</span>
            </div>
          </div>
        </div>

        <!-- Pro -->
        <div class="plan-card featured">
          <div class="plan-header">
            <h3 class="plan-name">Pro</h3>
            <p class="plan-description">Unlock the full capabilities of our character systems for a lower price</p>
          </div>
          <div class="plan-pricing">
            <span class="price">$25</span>
            <span class="period">Start from $25/month</span>
          </div>
          <button class="plan-button primary">Upgrade now</button>
          <div class="plan-features">
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Included NPCs</span>
              <span class="feature-value">3</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Trained global story content (NPCs)</span>
              <span class="feature-value">10</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Requests per month</span>
              <span class="feature-value">25K</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Per-client interactions and custom mode</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Per-client messaging and global</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Email support</span>
              <span class="feature-value">Yes</span>
            </div>
          </div>
        </div>

        <!-- Pro Plus -->
        <div class="plan-card">
          <div class="plan-header">
            <h3 class="plan-name">Pro Plus</h3>
            <p class="plan-description">For your game projects, perfect for more advanced operations</p>
          </div>
          <div class="plan-pricing">
            <span class="price">$60</span>
            <span class="period">Start from $60/month</span>
          </div>
          <button class="plan-button secondary">Upgrade now</button>
          <div class="plan-features">
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Included NPCs</span>
              <span class="feature-value">5</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Trained global story content (NPCs)</span>
              <span class="feature-value">200</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Requests per month</span>
              <span class="feature-value">100K</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Per-client interactions and custom mode</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Per-client messaging and global</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Priority implementation support</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">On-Prem deployment and infrastructure</span>
              <span class="feature-value">Yes</span>
            </div>
          </div>
        </div>

        <!-- Advanced -->
        <div class="plan-card">
          <div class="plan-header">
            <h3 class="plan-name">Advanced</h3>
            <p class="plan-description">For advanced users, indie games, and mid-scale RPG games on game servers</p>
          </div>
          <div class="plan-pricing">
            <span class="price">$250</span>
            <span class="period">Start from $250/month</span>
          </div>
          <button class="plan-button secondary">Upgrade now</button>
          <div class="plan-features">
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Included NPCs</span>
              <span class="feature-value">15</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Included global story content (NPCs)</span>
              <span class="feature-value">200</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Requests per month</span>
              <span class="feature-value">500K</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Per-client interactions and custom mode</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Per-client messaging and global</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Email support</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Priority implementation support</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">On-Prem deployment and infrastructure</span>
              <span class="feature-value">Yes</span>
            </div>
          </div>
        </div>

        <!-- Enterprise -->
        <div class="plan-card">
          <div class="plan-header">
            <h3 class="plan-name">Enterprise</h3>
            <p class="plan-description">For organizations, we offer customization for our services</p>
          </div>
          <div class="plan-pricing">
            <span class="price">Custom</span>
            <span class="period">Start from $500/month</span>
          </div>
          <button class="plan-button secondary">Contact sales</button>
          <div class="plan-features">
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Included NPCs</span>
              <span class="feature-value">-</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Included global story content (NPCs)</span>
              <span class="feature-value">-</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Requests per month</span>
              <span class="feature-value">-</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Per-client interactions and custom mode</span>
              <span class="feature-value">-</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Per-client messaging and global</span>
              <span class="feature-value">-</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Email support</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Priority implementation support</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">On-Prem deployment and infrastructure</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Dedicated customer service professional</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Unlimited creative server deployment</span>
              <span class="feature-value">Yes</span>
            </div>
            <div class="feature">
              <span class="feature-icon">✓</span>
              <span class="feature-text">Unlimited support operational</span>
              <span class="feature-value">Yes</span>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goToComingSoon = () => {
  router.push('/coming-soon')
}
</script>

<style scoped>
.pricing-page {
  font-family: 'Kanit', sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
}

/* Header Styles */
.header {
  padding: 1rem 2rem;
  position: relative;
  z-index: 10;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.nav-left {
  display: flex;
  align-items: center;
}

.logo {
  height: 32px;
  width: auto;
}

.nav-center {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 400;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: white;
}

.nav-right {
  display: flex;
  align-items: center;
}

.dashboard-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-family: 'Kanit', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dashboard-btn:hover {
  background: #4338ca;
}

/* Main Content */
.main {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.pricing-header {
  margin-bottom: 2rem;
}

.pricing-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
}

.pricing-tabs {
  display: flex;
  gap: 0.5rem;
}

.tab {
  background: rgba(255, 255, 255, 0.1);
  color: #a0a0a0;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-family: 'Kanit', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab.active {
  background: #4f46e5;
  color: white;
}

.pricing-plans {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.plan-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.plan-card.featured {
  border-color: #4f46e5;
  background: rgba(79, 70, 229, 0.1);
}

.plan-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.2);
}

.plan-header {
  margin-bottom: 1.5rem;
}

.plan-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.plan-description {
  color: #a0a0a0;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

.plan-pricing {
  margin-bottom: 1.5rem;
}

.price {
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.period {
  display: block;
  color: #a0a0a0;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.plan-button {
  width: 100%;
  padding: 0.75rem;
  border-radius: 8px;
  font-family: 'Kanit', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  margin-bottom: 1.5rem;
}

.plan-button.primary {
  background: #4f46e5;
  color: white;
}

.plan-button.primary:hover {
  background: #4338ca;
}

.plan-button.secondary {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.plan-button.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
}

.plan-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.85rem;
}

.feature-icon {
  color: #4ade80;
  font-weight: bold;
  width: 16px;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
  color: #e5e5e5;
}

.feature-value {
  color: #a0a0a0;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pricing-plans {
    grid-template-columns: 1fr;
  }
  
  .nav {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-center {
    order: 2;
  }
  
  .pricing-title {
    font-size: 1.5rem;
  }
}
</style>
