<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="logo-section">
        <img src="/assets/DeepNPC-Logo.png" alt="DeepNPC" class="logo" />
      </div>
      
      <div class="main-content">
        <h2 class="not-found-title">404</h2>
        <h3 class="not-found-subtitle">Page Not Found</h3>
        <p class="not-found-description">
          The page you're looking for doesn't exist yet.<br>
          We're working on building out all our features.
        </p>
        
        <div class="action-buttons">
          <button @click="goHome" class="btn btn-primary">
            ← Back to Home
          </button>
          <button @click="goBack" class="btn btn-secondary">
            Go Back
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  window.history.back()
}
</script>

<style scoped>
.not-found {
  font-family: 'Kanit', sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.not-found-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 800px;
  width: 100%;
  gap: 3rem;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.logo {
  height: 64px;
  width: auto;
  filter: drop-shadow(0 10px 20px rgba(79, 70, 229, 0.3));
}

.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.not-found-title {
  font-size: 6rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.not-found-subtitle {
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
  color: white;
}

.not-found-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #a0a0a0;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-family: 'Kanit', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid #4f46e5;
}

.btn-secondary:hover {
  background: #4f46e5;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .not-found-title {
    font-size: 4rem;
  }
  
  .not-found-subtitle {
    font-size: 1.5rem;
  }
  
  .not-found-description {
    font-size: 1rem;
  }
  
  .logo {
    height: 48px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 200px;
  }
}
</style>
