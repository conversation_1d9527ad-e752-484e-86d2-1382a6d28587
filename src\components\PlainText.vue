<template>
  <div class="plain-text-container">
    <pre class="plain-text-content">{{ content }}</pre>
  </div>
</template>

<script>
export default {
  name: 'PlainText',
  props: {
    filePath: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      content: 'Loading...',
      error: null
    }
  },
  async mounted() {
    try {
      const response = await fetch(this.filePath)
      if (!response.ok) {
        throw new Error(`Failed to load ${this.filePath}`)
      }
      this.content = await response.text()
    } catch (error) {
      this.error = error.message
      this.content = `Error loading content: ${error.message}`
    }
  }
}
</script>

<style scoped>
.plain-text-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Courier New', monospace;
}

.plain-text-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.6;
  font-size: 14px;
  color: #333;
  background-color: #f9f9f9;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

@media (max-width: 768px) {
  .plain-text-container {
    padding: 10px;
  }
  
  .plain-text-content {
    font-size: 12px;
    padding: 15px;
  }
}
</style>
