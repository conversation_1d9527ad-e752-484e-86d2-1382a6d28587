<template>
  <div class="app">
    <!-- Header -->
    <header class="header">
      <nav class="nav">
        <div class="nav-left">
          <img src="/assets/DeepNPC-Logo.png" alt="DeepNPC" class="logo" />
        </div>
        <div class="nav-center">
          <router-link to="/developers/documentation" class="nav-link">Docs</router-link>
          <router-link to="/sales/regular-pricing" class="nav-link">Pricing</router-link>
        </div>
        <div class="nav-right">
          <button @click="goToComingSoon" class="dashboard-btn">Dashboard</button>
        </div>
      </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
      <div class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">Create infinite new experiences</h1>
          <p class="hero-description">
            DeepNPC is a real-time cloud AI character service for video games.<br>
            Your characters will spin to life with ability to create relations,<br>
            converse, and remember experiences with your playerbase
          </p>
          <div class="hero-buttons">
            <button @click="goToComingSoon" class="btn btn-primary">Start your project</button>
            <button @click="goToComingSoon" class="btn btn-secondary">Request a demo</button>
          </div>
        </div>
        <div class="hero-visual">
          <img src="/assets/Floating-Background.png" alt="3D Visual" class="floating-bg" />
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-brand">
          <div class="footer-logo">
            <img src="/assets/DeepNPC-Logo.png" alt="DeepNPC" class="footer-logo-img" />
          </div>
          <div class="social-links">
            <a href="#" @click.prevent="goToComingSoon" class="social-link">📧</a>
            <a href="#" @click.prevent="goToComingSoon" class="social-link">💬</a>
            <a href="#" @click.prevent="goToComingSoon" class="social-link">🐦</a>
          </div>
        </div>

        <div class="footer-sections">
          <div class="footer-section">
            <h3 class="footer-section-title">Developers</h3>
            <router-link to="/developers/documentation" class="footer-link">Documentation</router-link>
            <router-link to="/developers/quickstart-guides" class="footer-link">Quickstart guides</router-link>
            <router-link to="/developers/support" class="footer-link">Support</router-link>
          </div>

          <div class="footer-section">
            <h3 class="footer-section-title">Sales</h3>
            <router-link to="/sales/regular-pricing" class="footer-link">Regular Pricing</router-link>
            <router-link to="/sales/business-pricing" class="footer-link">Business Pricing</router-link>
            <router-link to="/sales/tiered-pricing" class="footer-link">Tiered Pricing</router-link>
            <router-link to="/sales/request-demo" class="footer-link">Request a demo</router-link>
            <router-link to="/sales/contact-sales" class="footer-link">Contact sales</router-link>
          </div>

          <div class="footer-section">
            <h3 class="footer-section-title">Company documents</h3>
            <router-link to="/company/privacy-policy" class="footer-link">Privacy policy</router-link>
            <router-link to="/company/terms-of-service" class="footer-link">Terms of Service</router-link>
            <router-link to="/company/support-policy" class="footer-link">Support policy</router-link>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <p class="copyright">©2025 Lustre Technologies Limited. DeepNPC™</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goToComingSoon = () => {
  router.push('/coming-soon')
}
</script>

<style scoped>
.app {
  font-family: 'Kanit', sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.header {
  padding: 1rem 2rem;
  position: relative;
  z-index: 10;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo {
  height: 32px;
  width: auto;
}

.brand-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
}

.nav-center {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 400;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: white;
}

.nav-right {
  display: flex;
  align-items: center;
}

.dashboard-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-family: 'Kanit', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dashboard-btn:hover {
  background: #4338ca;
}

/* Main Content Styles */
.main {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 2rem;
}

.hero-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  align-items: center;
}

.hero-content {
  max-width: 600px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #a0a0a0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #a0a0a0;
  margin-bottom: 2rem;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-family: 'Kanit', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid #4f46e5;
}

.btn-secondary:hover {
  background: #4f46e5;
  transform: translateY(-2px);
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.floating-bg {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0 20px 40px rgba(79, 70, 229, 0.3));
}

/* Footer Styles */
.footer {
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 3rem 2rem 1rem;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  margin-bottom: 2rem;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.footer-logo-img {
  height: 24px;
  width: auto;
}

.footer-brand-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.footer-nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.footer-nav-link {
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 400;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-nav-link:hover {
  color: white;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  text-decoration: none;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
}

.footer-sections {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
}

.footer-link {
  color: #a0a0a0;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  cursor: pointer;
}

.footer-link:hover {
  color: white;
}

.footer-bottom {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.copyright {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .nav {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-center {
    order: 2;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-sections {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .footer-nav-links {
    gap: 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 200px;
  }
}
</style>
